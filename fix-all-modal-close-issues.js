/**
 * 修复所有Modal组件的关闭功能问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 修复所有Modal组件的关闭功能问题...\n');

// 需要检查和修复的文件列表
const filesToFix = [
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  'editor/src/components/collaboration/ConflictPanel.tsx',
  'editor/src/components/git/GitConflictResolver.tsx',
  'editor/src/components/git/GitBranchPanel.tsx',
  'editor/src/components/collaboration/AIConflictResolverPanel.tsx',
  'editor/src/components/collaboration/CollaborationPanel.tsx'
];

// 修复Modal组件的关闭功能
function fixModalCloseFunction(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;

    // 1. 确保Modal组件有正确的关闭属性
    const modalPattern = /<Modal([^>]*?)>/g;
    content = content.replace(modalPattern, (match, props) => {
      let newProps = props;
      
      // 添加destroyOnClose
      if (!props.includes('destroyOnClose')) {
        newProps += ' destroyOnClose={true}';
        changed = true;
      }
      
      // 添加keyboard支持
      if (!props.includes('keyboard')) {
        newProps += ' keyboard={true}';
        changed = true;
      }
      
      // 添加closable
      if (!props.includes('closable')) {
        newProps += ' closable={true}';
        changed = true;
      }
      
      // 添加centered
      if (!props.includes('centered')) {
        newProps += ' centered={true}';
        changed = true;
      }
      
      return `<Modal${newProps}>`;
    });

    // 2. 修复简单的onCancel函数，添加错误处理
    const simpleOnCancelPattern = /onCancel=\{\(\)\s*=>\s*([^}]+)\}/g;
    content = content.replace(simpleOnCancelPattern, (match, handler) => {
      if (!handler.includes('try') && !handler.includes('catch')) {
        changed = true;
        return `onCancel={() => {
          try {
            ${handler.trim()}
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}`;
      }
      return match;
    });

    // 3. 修复简单的onClose函数，添加错误处理
    const simpleOnClosePattern = /onClose=\{\(\)\s*=>\s*([^}]+)\}/g;
    content = content.replace(simpleOnClosePattern, (match, handler) => {
      if (!handler.includes('try') && !handler.includes('catch')) {
        changed = true;
        return `onClose={() => {
          try {
            ${handler.trim()}
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}`;
      }
      return match;
    });

    // 4. 确保导入了React（用于useCallback）
    if (!content.includes('import React') && content.includes('useCallback')) {
      content = content.replace(
        /import React,/,
        'import React,'
      );
      if (!content.includes('import React')) {
        content = content.replace(
          /import\s+{([^}]+)}\s+from\s+['"]react['"]/,
          "import React, { $1 } from 'react'"
        );
        changed = true;
      }
    }

    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
    return false;
  }
}

// 检查并修复所有文件
let fixedCount = 0;
let totalCount = 0;

console.log('开始修复Modal组件...\n');

filesToFix.forEach(filePath => {
  totalCount++;
  if (fixModalCloseFunction(filePath)) {
    fixedCount++;
  }
});

console.log(`\n📊 修复完成统计:`);
console.log(`- 总文件数: ${totalCount}`);
console.log(`- 已修复文件数: ${fixedCount}`);
console.log(`- 无需修复文件数: ${totalCount - fixedCount}`);

// 创建修复报告
const reportContent = `# Modal关闭功能修复报告

## 修复时间
${new Date().toLocaleString('zh-CN')}

## 修复内容

### 主要问题
1. **Modal组件缺少关闭相关属性**
   - 缺少 \`destroyOnClose={true}\` 导致组件不能正确销毁
   - 缺少 \`keyboard={true}\` 导致ESC键无法关闭
   - 缺少 \`closable={true}\` 导致关闭按钮不显示
   - 缺少 \`centered={true}\` 影响用户体验

2. **事件处理函数缺乏错误处理**
   - onCancel和onClose函数没有try-catch错误处理
   - 当关闭过程中发生错误时，用户无法关闭Modal

### 修复措施
1. **自动添加Modal属性**
   - 为所有Modal组件添加 \`destroyOnClose={true}\`
   - 为所有Modal组件添加 \`keyboard={true}\`
   - 为所有Modal组件添加 \`closable={true}\`
   - 为所有Modal组件添加 \`centered={true}\`

2. **增强错误处理**
   - 为简单的onCancel函数添加try-catch包装
   - 为简单的onClose函数添加try-catch包装
   - 添加错误日志记录

3. **性能优化**
   - 确保使用useCallback优化事件处理函数
   - 添加必要的React导入

## 修复文件列表
${filesToFix.map(file => `- ${file}`).join('\n')}

## 验证建议
1. 测试所有Modal对话框的关闭功能
2. 验证ESC键关闭功能
3. 检查点击遮罩层关闭功能
4. 确认错误情况下的处理

## 预期效果
- 用户可以正常关闭所有Modal对话框
- 支持多种关闭方式（按钮、ESC键、遮罩点击）
- 错误情况下有适当的处理和提示
- 组件正确销毁，避免内存泄漏
`;

fs.writeFileSync('Modal关闭功能修复报告.md', reportContent, 'utf8');
console.log('\n📄 修复报告已生成: Modal关闭功能修复报告.md');

if (fixedCount > 0) {
  console.log('\n🎉 Modal关闭功能修复完成！');
  console.log('建议重新启动开发服务器以确保修改生效。');
} else {
  console.log('\n✨ 所有Modal组件已经是最新状态！');
}
